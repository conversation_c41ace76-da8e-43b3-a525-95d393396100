<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
import { billCheckBatch, billCreateBatch, queryTotalBillList } from '_/api/payBillManage'

export default defineComponent({
  data() {
    return {
      pageCode: 'payBillManage',
      schema: {} as any,
      confirmBillVisible: false, // 确认账单付款
      preExtVisible: false, // 存在未核销预付款提示
      preExt: '', // 存在未核销完成的预付款==0 不存在(详情弹窗)。===1:存在（提示弹窗）
      batchData: {
        range: '-',
        prepayAmount: '-',
        verifiedAmount: '-',
        unVerifyAmount: '-',
        bankName: '-',
        settleAmount: '-',
        payedAmount: '-',
      }, // 弹窗内容
      preExtTitleData: {}, // 标题依赖的数据
      tableColums: [
        {
          dataIndex: 'orderNo',
          title: '账单编号',
        },
        {
          dataIndex: 'businessType,cardType',
          title: '业务类型\n卡类型',
          scopedSlots: { customRender: 'businessTypeCard' }
        },
        {
          dataIndex: 'oracleCode,specialBusinessDesc',
          title: 'Oracle编码\n特殊业务',
          scopedSlots: { customRender: 'oracleSpecial' }
        },
        {
          dataIndex: 'regulateServiceAmount,payedServiceAmount',
          title: '调账后手续费\n已付款手续费',
          scopedSlots: { customRender: 'payedServiceAmount' }
        },
        {
          dataIndex: 'paymentStatusText,billType',
          title: '付款状态\n账单类型',
          scopedSlots: { customRender: 'paymentBillType' }
        },
        {
          dataIndex: 'currentPayAmount',
          title: '本期付款金额',
          scopedSlots: { customRender: 'currentPayAmount' }
        },
      ],
      // 多行插槽名称数组 - 统一管理所有需要多行显示的插槽
      multiLineSlots: ['businessTypeCard', 'oracleSpecial', 'payedServiceAmount', 'paymentBillType'],
      selectRowData: [] as any[], // 列表选中的一项或多项
      tableData: [] as any[],
      selectedRowKeys: [] as Array<number>, // 选中行的key数组
      selectedRows: [] as Array<object>, // 选中行的key数组
      payAmountErrors: {} as Record<string, string>, // 存储每行的付款金额错误信息
      BUSINESS_TYPE_ENUM: [] as Array<{label: string, value: string}>,
      CARD_TYPE_ENUM: [] as Array<{label: string, value: string}>,
      BILL_TYPE_ENUM: [
        {
          label: '结算账单',
          value: 'SETTLE',
        },
        {
          label: '预付账单',
          value: 'PREPAY',
        }
      ],
      PAYMENT_ENUM: [
        {
          label: '未付款',
          value: 'UN_PAY'
        },
        {
          label: '付款中',
          value: 'PAYING'
        },
        {
          label: '部分付款',
          value: 'PARTIAL_PAYED'
        },
        {
          label: '已付款',
          value: 'PAYED'
        },
      ],
      glDate: '',
      remark: '',
      subDisable: false, // 禁止提交
    }
  },
  computed: {
    renderer() {
      return this[`__yeepay_lowcode_${this.pageCode}__`].value
      // return this.__yeepay_lowcode_payBillManage__.value
    },
    // 选中账单的应付总金额
    getPayableAmount() {
      // 使用工具函数计算应付金额，避免浮点数精度问题
      const payableAmounts = this.selectedRows.map((item: any) => {
        return (item.regulateServiceAmount || 0) - (item.payedServiceAmount || 0) - (item.verifiedServiceAmount || 0)
      })
      const result = this.calculateAmount(payableAmounts)
      // this.dealTotalPaymentAmount = result
      return result
    },
    // 计算选中账单的本期付款总金额
    getPaymentAmount() {
      // 避免浮点数精度问题：先转换为分（整数）进行计算
      const totalCents = this.selectedRows.reduce((total: number, row: any) => {
        const amountCents = Math.round((row.currentPayAmount || 0) * 100)
        return total + amountCents
      }, 0)
      const result = totalCents / 100
      this.subDisable = result <= 0
      // this.realTotalPaymentAmount = result
      return result
    },
    modalTitle() {
      const data = this.preExtTitleData as any
      return `${data.selectBankName || ''} - ${data.selectInstitutionType || ''} 存在未核销预付款，请检查！`
    },
  },
  methods: {
    // 金额计算工具函数 - 避免浮点数精度问题
    calculateAmount(amounts: number[]): number {
      const totalCents = amounts.reduce((total, amount) => {
        return total + Math.round((amount || 0) * 100)
      }, 0)
      return Math.round(totalCents) / 100
    },

    clearModalData() {
      this.tableData = []
      this.selectedRowKeys = []
      this.selectedRows = []
      this.payAmountErrors = {}
    },
    // 获取所有枚举集合
    getDicts(data: any) {
      const res = data && JSON.parse(data)
      this.BUSINESS_TYPE_ENUM = res['BUSINESS_TYPE'].map((item: any) => {
        item.label = item.name
        item.value = item.code
        return item
      }) // 业务类型
      this.CARD_TYPE_ENUM = res['CARD_TYPE'].map((item: any) => {
        item.label = item.name
        item.value = item.code
        return item
      }) // 卡类型
    },
    filterNumber(value: any) {
      return (value === 0 || value === '0' || value) ? value : '-'
    },
    // 枚举值
    getEnumValue(enums: any, v: any) {
      return (enums.find((item: any) => item.value === v) || {}).label || '-'
    },
    openModal() {
      // const flag = rows.every((row: any) => ['CHECKED'].includes(row.accountingCheckStatus) && ['PARTIAL_PAYED', 'UN_PAY'].includes(row.paymentStatus) && row.billType === 'SETTLE') || false
      if (this.preExt === '0') {
      // 不存在
        this.showDrawer()
      } else {
        // 存在未核销付款单
        this.showConfirm()
      }
    },
    // 付款单预校验
    isOpenConfirmBill(rowData: any, preExtTitleData: any) {
      // 初始化
      this.clearModalData()
      this.preExtTitleData = preExtTitleData && JSON.parse(preExtTitleData) || {}
      this.preExt = ''
      this.selectRowData = rowData && JSON.parse(rowData) || []

      // 提取订单号
      const orders = this.selectRowData.map((item: any) => item.orderNo)

      billCheckBatch({
        prepayOrderNos: orders.join(',')
        // prepayOrderNos: '*****************'
      }).then((res) => {
        const data = res.data
        this.preExt = data.preExt

        this.batchData = {
          ...data,
          range: data.startTime + ' ~ ' + data.endTime,
        }

        this.tableData = data.configCostPaymentBillEntities.map((i: any) => {
          // ['CHECKED'].includes(row.accountingCheckStatus) && ['PARTIAL_PAYED','UN_PAY'].includes(row.paymentStatus)
          const canCheck = ['CHECKED'].includes(i.accountingCheckStatus) && ['PARTIAL_PAYED', 'UN_PAY'].includes(i.paymentStatus)

          return {
            ...i,
            blank: '\u200B',
            canCheck,
            businessType: this.getEnumValue(this.BUSINESS_TYPE_ENUM, i.businessType),
            cardType: this.getEnumValue(this.CARD_TYPE_ENUM, i.cardType),
            paymentStatusText: this.getEnumValue(this.PAYMENT_ENUM, i.paymentStatus), // 显示文本
            paymentStatus: i.paymentStatus, // 保留原始值用于判断
            billType: this.getEnumValue(this.BILL_TYPE_ENUM, i.billType),
            specialBusinessDesc: i.specialBusiness ? '是' : '否',
            currentPayAmount: 0 // 初始化本期付款金额
          }
        }) || []
        this.openModal()
      }).catch(() => { })
    },
    afterVisibleChange(val: boolean) {
      console.log('visible', val)
    },
    onSelectChange(selectedRowKeys: Array<number>, selectedRows: Array<object>) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    // 控制复选框的禁用状态
    getCheckboxProps(record: any) {
      return {
        disabled: record.paymentStatus === 'PAYED', // 已付款状态禁用选择
        name: record.orderNo,
      }
    },
    // 处理本期付款金额输入
    handlePayAmountChange(value: any, record: any, index: number) {
      const numValue = Number(value) || 0
      const rowKey = record.orderNo || index // 使用订单号作为唯一标识

      // 付款金额 !>（调账后手续费-已付款手续费）
      const maxAmount = (record.regulateServiceAmount || 0) - (record.payedServiceAmount || 0) - (record.verifiedServiceAmount || 0)

      // 清除之前的错误信息
      this.$delete(this.payAmountErrors, rowKey)

      if (numValue > maxAmount) {
        // 设置错误信息，但不阻止输入
        this.$set(this.payAmountErrors, rowKey, `本期付款金额不能大于${maxAmount}元`)
        // this.$set(this.payAmountErrors, rowKey, `本期付款金额不能大于${maxAmount}元（调账后手续费-已付款手续费-已核销手续费）`)
      }

      const newData = [...this.tableData] as any[]
      if (newData[index]) {
        newData[index].currentPayAmount = numValue
        this.tableData = newData
      }
      // 更新数据
      const selectedIndex = this.selectedRows.findIndex((row: any) => row === record)
      if (selectedIndex !== -1) {
        (this.selectedRows[selectedIndex] as any).currentPayAmount = numValue
      }
    },

    // 统一校验付款金额
    validatePayAmount(): boolean {
      // 1. 校验是否选中了账单
      if (this.selectedRows.length === 0) {
        this.$message.error('请先选择要付款的账单')
        return false
      }

      // 2. 校验是否有金额输入错误
      const hasAmountErrors = Object.keys(this.payAmountErrors).length > 0
      if (hasAmountErrors) {
        this.$message.error('请先修正付款金额错误')
        return false
      }

      // 3. 校验选中行的付款金额必须大于0
      const invalidRows = this.selectedRows.filter((row: any) => {
        return (row.currentPayAmount || 0) <= 0
      })

      if (invalidRows.length > 0) {
        this.$message.error('勾选的账单中，付款金额必须大于0')
        return false
      }

      // 4. 校验付款金额不能超过应付金额
      const exceedRows = this.selectedRows.filter((row: any) => {
        const maxAmount = (row.regulateServiceAmount || 0) - (row.payedServiceAmount || 0)
        return (row.currentPayAmount || 0) > maxAmount
      })

      if (exceedRows.length > 0) {
        this.$message.error('存在付款金额超过可付金额的账单，请检查')
        return false
      }

      return true
    },
    refresh() {
      this.renderer.runQuery('queryTotalBillList')
    },

    // 付款
    payment(isConfirm: boolean) {
      const prepayOrderNos = this.selectedRows.map((row: any) => row.orderNo) // 批量父级订单号集合
      const subReqVoList = this.selectedRows.map((item: any) => {
        return {
          amount: item.regulateServiceAmount, // 调账后手续费（付款金额）
          totalBillNo: item.superOrderNo, // 父级订单号
          configId: item.configId, // 配置编码
          toPayAmount: item.currentPayAmount, // 账单剩余金额（本期付款金额）
          subBillNo: item.orderNo, // 子账单号
        }
      })

      billCreateBatch({
        isConfirm,
        realTotalPaymentAmount: this.getPaymentAmount, // 选中-实付
        glDate: this.glDate,
        prepayOrderNos: prepayOrderNos.join(','),
        subReqVoList, // 选中的列
        dealTotalPaymentAmount: this.getPayableAmount, // 选中-应付
        remark: this.remark,
      }).then((res) => {
        console.log('付款成功:', res.data)
        this.$message.success('付款提交成功')
        this.onClose()
        this.refresh()
        // 可能需要刷新列表数据
      }).catch((error) => {
        console.error('付款失败:', error)
        this.$message.error('付款提交失败，请重试')
      })
    },

    // 执行付款校验并执行回调
    verificationPayAmount(fn: Function) {
      if (this.validatePayAmount()) {
        fn()
      }
    },
    // 线下付款
    offlinePayConfirm() {
      this.verificationPayAmount(() => {
        if (!this.glDate) {
          this.$message.error('请填写GL日期！')
          return
        }
        this.payment(true)
      })
    },
    // 确认付款
    paymentConfirm() {
      this.verificationPayAmount(() => {
        this.payment(false)
      })
    },
    onChangeGL(val: string) {
      this.glDate = val
    },
    // 弹窗确认按钮
    handleOk() {
      this.handleCancel()
      this.showDrawer()
    },
    // 列表弹窗开
    showDrawer() {
      this.confirmBillVisible = true
    },
    // 列表弹窗关
    onClose() {
      this.confirmBillVisible = false
    },
    // 未核销弹窗开
    showConfirm() {
      this.preExtVisible = true
    },
    // 未核销弹窗开关
    handleCancel() {
      this.preExtVisible = false
    },
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
    // this.isOpenConfirmBill(null, null)
    // this.showDrawer()
  }
})
</script>
<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" :id="pageCode" :schema="schema" @openModal="isOpenConfirmBill" @getDicts="getDicts"/>
    <a-modal
      width="560px"
      :title="modalTitle"
      :visible="preExtVisible"
      ok-text="无需核销"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-descriptions title=" " :column="2">
        <a-descriptions-item label="预付周期">
          {{ batchData.range || '-' }}
        </a-descriptions-item>
        <br/>
        <a-descriptions-item label="预付金额">
          {{ filterNumber(batchData.prepayAmount) }}
        </a-descriptions-item>
        <br/>
        <a-descriptions-item label="已核销金额">
          {{ filterNumber(batchData.verifiedAmount) }}
        </a-descriptions-item>
        <a-descriptions-item label="未核销金额">
          {{ filterNumber(batchData.unVerifyAmount) }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="备注信息">
          {{ batchData.remark }}
        </a-descriptions-item> -->
      </a-descriptions>
    </a-modal>
    <a-drawer
      title="请确认账单是否可以提交付款？"
      placement="right"
      :visible="confirmBillVisible"
      :after-visible-change="afterVisibleChange"
      width="900"
      class="payBillManage"
      @close="onClose"
    >
      <div class="payBillManageFlex">
        <div class="drawer-content">
          <a-descriptions title=" " :column="3">
            <a-descriptions-item label="收款方名称">
              {{ filterNumber(batchData.bankName) }}
            </a-descriptions-item>
            <br/>
            <br/>
            <a-descriptions-item label="应付款金额">
              {{ filterNumber(batchData.settleAmount) }}
            </a-descriptions-item>
            <a-descriptions-item label="已付款金额">
              {{ filterNumber(batchData.payedAmount) }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        <br/>
        <a-table
          class="paybill-table"
          :columns="tableColums"
          :dataSource="tableData"
          :scroll="{ x: '100%' }"
          :pagination="false"
          bordered
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
            getCheckboxProps: getCheckboxProps
          }"
        >
          <template
            v-for="slotName in multiLineSlots"
            :slot="slotName"
            slot-scope="text, record, index, column"
          >
            <div v-for="field in column.dataIndex.split(',')" :key="`${slotName}-${field}`">
              {{ filterNumber(record[field]) }}
            </div>
          </template>

          <template slot="currentPayAmount" slot-scope="text, record, index">
            <div>
              <!-- 已付款状态显示 "-"，其他状态显示输入框 -->
              <span v-if="record.paymentStatus === 'PAYED'" class="disabled-text">-</span>
              <div v-else>
                <a-input-number
                  :value="record.currentPayAmount"
                  :min="0"
                  :precision="2"
                  placeholder="请输入金额"
                  style="width: 100%"
                  :class="{ 'has-error': payAmountErrors[record.orderNo || index] }"
                  @change="(value) => handlePayAmountChange(value, record, index)"
                />
                <div
                  v-if="payAmountErrors[record.orderNo || index]"
                  class="error-message"
                >
                  {{ payAmountErrors[record.orderNo || index] }}
                </div>
              </div>
            </div>
          </template>
        </a-table>
        <div class="p-text">
          <span>选中账单应付金额：</span>{{ getPayableAmount }}
        </div>
        <div class="p-text">
          <span>选中账单本期付款金额：</span>{{ getPaymentAmount }}
        </div>
        <div class="p-text">
          <span>GL日期：</span><a-date-picker :model="glDate" format="yy-MM-DD" valueFormat="yy-MM-DD" @change="onChangeGL" />
        </div>
        <div class="p-text">
          <span>付款备注：</span>
          <a-textarea
            v-model="remark"
            style="width: 360px"
            placeholder="最大可输入100字符"
            maxlength="100"
            :auto-size="{ minRows: 4 }"
          />
        </div>
      </div>
      <!-- 底部按钮 -->
      <div class="drawer-footer">
        <a-button style="margin-right: 10px" @click="onClose">
          取消
        </a-button>
        <a-button style="margin-right: 10px" :disabled="subDisable" type="primary" @click="offlinePayConfirm">
          线下付款
        </a-button>
        <a-button type="primary" :disabled="subDisable" @click="paymentConfirm">
          付款
        </a-button>
      </div>
    </a-drawer>
  </div>
</template>
<style lang="less" scoped>
.paybill-table{
  margin-bottom: 16px;
  ::v-deep .ant-table-thead > tr > th,
  ::v-deep .ant-table-tbody > tr > td{
    padding: 8px 8px;
    // background: transparent;
  }

  // 表头换行样式
  ::v-deep .ant-table-thead > tr > th {
    white-space: pre-line;
    // text-align: center;
    line-height: 1.4;
  }

  // 表格内容换行样式
  ::v-deep .ant-table-tbody > tr > td {
    div > div {
      line-height: 1.4;
      margin-bottom: 2px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.payBillManageFlex{
  padding-bottom: 50px
}
// 错误状态样式
.has-error {
  ::v-deep .ant-input-number {
    border-color: #ff4d4f;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
  }
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  line-height: 1.5;
  margin-top: 4px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 24px;
  border-top: 1px solid #e8e8e8;
  background: #fff;
  text-align: right;
}

.p-text {
  display: flex;
  align-items: center;
  margin: 14px 0;
  span {
    text-align: right;
    width: 164px;
  }
}

// 禁用状态文本样式
.disabled-text {
  color: #d9d9d9;
  font-size: 14px;
  text-align: center;
  display: inline-block;
  width: 100%;
}
</style>
