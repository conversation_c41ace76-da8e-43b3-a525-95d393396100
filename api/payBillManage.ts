import request from '_/utils/request'
type billCheckBatchReq = {

}
/**
 * 批量生成付款单预校验回调参数
 */
type billCheckBatchRes = {
  data: {
    preExt: string,
    startTime: string,
    endTime: string,
    range: string,
    prepayAmount: string,
    verifiedAmount: string,
    unVerifyAmount: string,
    bankName: string,
    settleAmount: string,
    payedAmount: string,
    configCostPaymentBillEntities: Array<[]>
  }
}

/**
 * 批量生成付款单
 */
type billCreateBatchReq = {
}

/**
 * 批量生成付款单回调参数
 */
type billCreateBatchRes = {
  data: object

}

/**
 * 付款账单列表
 */
type queryTotalBillListReq = {
}

/**
 * 付款账单列表回调参数
 */
type queryTotalBillListRes = {
  data: object

}

/**
 * @description: 批量生成付款单预校验
 * @param {billCheckBatchReq} data - 请求体
 * @returns { Promise<billCheckBatchRes> } - 返回体
 */
export const billCheckBatch = async (data: billCheckBatchReq): Promise<billCheckBatchRes> => {
  const res = await request.post('/finance-process-hessian/financeProcess/payment/bill/checkBatch', data)
  return (await res).data
}

/**
 * @description: 批量生成付款单
 * @param {billCreateBatchReq} data - 请求体
 * @returns { Promise<billCreateBatchRes> } - 返回体
 */
export const billCreateBatch = async (data: billCreateBatchReq): Promise<billCreateBatchRes> => {
  const res = await request.post('/finance-process-hessian/financeProcess/payment/bill/createBatch', data)
  return (await res).data
}

/**
 * @description: 付款账单列表
 * @param {queryTotalBillListReq} data - 请求体
 * @returns { Promise<queryTotalBillListRes> } - 返回体
 */
export const queryTotalBillList = async (data: queryTotalBillListReq): Promise<queryTotalBillListRes> => {
  const res = await request.post('/zjpt-boss/bankCost/totalBill/queryTotalBillList', data)
  return (await res).data
}
